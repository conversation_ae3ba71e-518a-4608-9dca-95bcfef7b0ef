import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import { Sparkles } from 'lucide-react';

interface MessageItemProps {
  message: {
    text: string;
    sender: 'user' | 'ai';
  };
  isLoading?: boolean;
  isStreaming?: boolean; // 新增：是否使用流式输出
  progressStage?: string; // 新增：进度阶段
  progressPercentage?: number; // 新增：进度百分比
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  isLoading,
  isStreaming = false,
  progressStage,
  progressPercentage
}) => {
  const isUser = message.sender === 'user';
  const [displayText, setDisplayText] = useState('');
  const [charIndex, setCharIndex] = useState(0);

  // 进度头像组件 - 参考Lottie设计的双层圆环
  const ProgressAvatar: React.FC<{ stage?: string; percentage?: number }> = ({ stage, percentage = 0 }) => {
    const strokeWidth = 3.5;
    const radius = 15;
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference * 0.75; // 显示25%的圆弧

    return (
      <div className="relative w-10 h-10 flex items-center justify-center">
        {/* 参考Lottie的双层圆环设计 */}
        <svg
          width="36"
          height="36"
          viewBox="0 0 36 36"
        >
          <defs>
            {/* 背景圆环渐变 */}
            <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#e2e8f0" />
              <stop offset="100%" stopColor="#cbd5e1" />
            </linearGradient>

            {/* 前景流动渐变 */}
            <linearGradient id="flowingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#1e40af" />
              <stop offset="25%" stopColor="#3b82f6" />
              <stop offset="50%" stopColor="#60a5fa" />
              <stop offset="75%" stopColor="#93c5fd" />
              <stop offset="100%" stopColor="#1e40af" />

              {/* 渐变旋转动画 - 参考Lottie的旋转效果 */}
              <animateTransform
                attributeName="gradientTransform"
                type="rotate"
                values="0 18 18;360 18 18"
                dur="3s"
                repeatCount="indefinite"
              />
            </linearGradient>
          </defs>

          {/* 背景圆环 - 低透明度，静态 */}
          <circle
            cx="18"
            cy="18"
            r={radius}
            fill="none"
            stroke="url(#backgroundGradient)"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            opacity="0.15"
          />

          {/* 前景圆环 - 参考Lottie的路径修剪和旋转效果 */}
          <circle
            cx="18"
            cy="18"
            r={radius}
            fill="none"
            stroke="url(#flowingGradient)"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            opacity="0.9"
            transform="rotate(-90 18 18)"
          >
            {/* 旋转动画 - 模拟Lottie的旋转效果 */}
            <animateTransform
              attributeName="transform"
              type="rotate"
              values="-90 18 18;270 18 18;-90 18 18"
              dur="2s"
              repeatCount="indefinite"
            />
          </circle>
        </svg>
      </div>
    );
  };

  // 检测内容类型，用于优化渲染
  const detectContentType = (text: string): 'structured' | 'conversational' | 'general' => {
    // 检测结构化内容特征
    const structuredIndicators = [
      /^#+\s/m,  // 标题
      /^\s*[-*+]\s/m,  // 无序列表
      /^\s*\d+\.\s/m,  // 有序列表
      /\*\*.*?\*\*/,  // 粗体
      /```/,  // 代码块
    ];

    let structuredCount = 0;
    for (const pattern of structuredIndicators) {
      if (pattern.test(text)) {
        structuredCount++;
      }
    }

    if (structuredCount >= 2) {
      return 'structured';
    }

    // 检测对话式内容特征
    const conversationalIndicators = [
      /[？?]/,  // 问号
      /您|你/,  // 称呼
      /比如|例如/,  // 举例
      /建议|推荐/,  // 建议性语言
    ];

    let conversationalCount = 0;
    for (const pattern of conversationalIndicators) {
      if (pattern.test(text)) {
        conversationalCount++;
      }
    }

    if (conversationalCount >= 2) {
      return 'conversational';
    }

    return 'general';
  };
  
  // 当收到新消息时，模拟打字效果
  useEffect(() => {
    if (!isUser && message.text && isStreaming) {
      setDisplayText('');
      setCharIndex(0);

      const typingInterval = setInterval(() => {
        setCharIndex(prevIndex => {
          const nextIndex = prevIndex + 1;
          if (nextIndex <= message.text.length) {
            setDisplayText(message.text.substring(0, nextIndex));
            return nextIndex;
          } else {
            clearInterval(typingInterval);
            return prevIndex;
          }
        });
      }, 15); // 调整速度

      return () => clearInterval(typingInterval);
    } else if (!isUser && !isStreaming) {
      // 非流式模式，直接显示全部文本
      setDisplayText(message.text);
    }
  }, [isUser, message.text, isStreaming]); // 移除 charIndex 依赖

  const renderContent = () => {
    if (isLoading && !isUser) {
      // 加载时不显示内容，进度已在头像中展示
      return null;
    } else if (!isUser) {
      // AI message - 使用流式显示的文本或完整文本
      const textToRender = isStreaming ? displayText : message.text;
      
      // 检测当前消息的内容类型
      const contentType = detectContentType(textToRender);

      // 根据内容类型调整样式类名
      const getContainerClass = (type: string) => {
        const baseClass = "prose prose-sm max-w-none";
        switch (type) {
          case 'structured':
            return `${baseClass} structured-content`;
          case 'conversational':
            return `${baseClass} conversational-content`;
          default:
            return `${baseClass} general-content`;
        }
      };

      // 根据内容类型调整段落间距
      const getParagraphComponent = (type: string) => {
        switch (type) {
          case 'structured':
            return ({ children }: any) => (
              <p className="mb-3 leading-relaxed text-gray-900">{children}</p>
            );
          case 'conversational':
            return ({ children }: any) => (
              <p className="mb-2 leading-normal text-gray-900">{children}</p>
            );
          default:
            return ({ children }: any) => (
              <p className="mb-2 leading-relaxed text-gray-900">{children}</p>
            );
        }
      };

      return (
        <div className={getContainerClass(contentType)}>
          <ReactMarkdown
            remarkPlugins={[remarkGfm, remarkBreaks]}
            components={{
              p: getParagraphComponent(contentType),

              // 标题组件 - 根据内容类型调整样式
              h1: ({ children }) => {
                const isStructured = contentType === 'structured';
                return (
                  <h1 className={`text-xl font-bold text-gray-900 ${
                    isStructured ? 'mb-4 mt-6 border-b border-gray-200 pb-2' : 'mb-3 mt-4'
                  }`}>
                    {children}
                  </h1>
                );
              },
              h2: ({ children }) => {
                const isStructured = contentType === 'structured';
                return (
                  <h2 className={`text-lg font-semibold text-gray-900 ${
                    isStructured ? 'mb-3 mt-5' : 'mb-2 mt-3'
                  }`}>
                    {children}
                  </h2>
                );
              },
              h3: ({ children }) => (
                <h3 className="text-base font-medium mb-2 mt-2 text-gray-900">{children}</h3>
              ),

              // 列表组件 - 根据内容类型调整间距
              ul: ({ children }) => {
                const isStructured = contentType === 'structured';
                return (
                  <ul className={`ml-4 text-gray-900 ${
                    isStructured ? 'mb-4 space-y-2' : 'mb-3 space-y-1'
                  }`}>
                    {children}
                  </ul>
                );
              },
              ol: ({ children }) => {
                const isStructured = contentType === 'structured';
                return (
                  <ol className={`ml-4 text-gray-900 ${
                    isStructured ? 'mb-4 space-y-2' : 'mb-3 space-y-1'
                  }`}>
                    {children}
                  </ol>
                );
              },
              li: ({ children }) => (
                <li className="leading-relaxed text-gray-900">{children}</li>
              ),

              // 强调文本 - 自然风格（仿照 **比如** 的效果）
              strong: ({ children }) => (
                <strong className="font-semibold text-gray-900">{children}</strong>
              ),

              // 代码块和行内代码 - 只对真正的代码块应用样式
              code: ({ children, className, ...props }: any) => {
                // 如果是行内代码（没有className或不是language-开头），应用代码样式
                if (!className || !className.startsWith('language-')) {
                  return <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">{children}</code>;
                }
                // 否则是代码块内的代码，不应用特殊样式
                return <code {...props}>{children}</code>;
              },
              pre: ({ children }) => {
                // 检查是否真的是代码块（包含language-类名的code元素）
                const hasCodeBlock = React.Children.toArray(children).some(child =>
                  React.isValidElement(child) &&
                  (child.props as any)?.className?.startsWith?.('language-')
                );

                if (hasCodeBlock) {
                  return <pre className="bg-gray-100 p-3 rounded mb-3 overflow-x-auto">{children}</pre>;
                }
                // 否则是普通的pre元素，不应用代码块样式
                return <div>{children}</div>;
              },
            }}
          >
            {textToRender}
          </ReactMarkdown>
          {isStreaming && charIndex < message.text.length && (
            <span className="inline-block w-1 h-4 bg-blue-500 ml-1 animate-pulse"></span>
          )}
        </div>
      );
    } else {
      // User message - Gemini 风格的用户消息
      return (
        <div className="user-message-text" style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit', margin: 0 }}>
          {message.text}
        </div>
      );
    }
  };

  // 用户消息 - Gemini风格，右对齐，蓝色背景
  if (isUser) {
    return (
      <div className="flex w-full justify-end mb-8">
        <div className="flex flex-col max-w-[85%]">
          <div className="bg-blue-600 text-white px-5 py-4 rounded-2xl shadow-sm">
            {renderContent()}
          </div>
        </div>
      </div>
    );
  } else {
    // AI消息 - Gemini风格，无边框，带图标和状态信息
    return (
      <div className="flex w-full justify-start mb-8">
        <div className="flex flex-col max-w-[85%]">
          {/* 顶部：图标和状态信息 */}
          <div className="flex items-center space-x-3 mb-2">
            {/* AI图标 */}
            <div className="flex-shrink-0">
              {(isLoading || (progressStage && progressPercentage !== undefined && progressPercentage < 100)) ? (
                <ProgressAvatar stage={progressStage} percentage={progressPercentage || 0} />
              ) : (
                <div className="w-10 h-10 flex items-center justify-center">
                  <Sparkles className="w-6 h-6 text-purple-600" />
                </div>
              )}
            </div>

            {/* 状态信息 */}
            <div className="text-sm text-gray-600">
              由己小助手
            </div>
          </div>

          {/* 正文内容 - 从图标下方开始，调整左边距适应更大的头像 */}
          <div style={{ marginLeft: '3.25rem' }}>
            {renderContent()}
          </div>
        </div>
      </div>
    );
  }
};

export default MessageItem;
